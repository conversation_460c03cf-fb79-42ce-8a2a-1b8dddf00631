; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-s3-dev]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

monitor_speed = 115200
upload_speed = 921600
build_flags =
  -DARDUINO_USB_MODE=1
  -DARDUINO_USB_CDC_ON_BOOT=0
  -DUSB_VID=0x303A
  -DUSB_PID=0x1001
lib_deps =
    me-no-dev/AsyncTCP@1.1.1
    esphome/ESPAsyncWebServer-esphome@3.3.0
    ricki-z/SDS011 sensor Library@0.0.8
    lewapek/Nova Fitness Sds dust sensors library@1.5.1
    adafruit/DHT sensor library@1.4.6
    knolleary/PubSubClient@2.8
    marcoschwartz/LiquidCrystal_I2C@1.1.4
    bblanchon/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@6.21